#!/bin/bash

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 从.env文件加载变量
if [ -f .env ]; then
  export $(grep -E "^DOCKER_SWARM_NAME=|^DOCKER_SWARM_FILE=|^DOCKER_IMAGE_NAME=|^DOCKER_REGISTRY=|^DOCKER_KEEP_IMAGES=" .env | xargs)
fi

# 设置默认值
DOCKER_SWARM_NAME=${DOCKER_SWARM_NAME:-fxerp-test}
DOCKER_COMPOSE_FILE=${DOCKER_COMPOSE_FILE:-deploy.test.yml}
DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME:-fxerp}
DOCKER_REGISTRY=${DOCKER_REGISTRY:-}
DOCKER_KEEP_IMAGES=${DOCKER_KEEP_IMAGES:-3}

# 生成镜像标签（使用时间戳）
DOCKER_IMAGE_TAG=$(date +"%Y%m%d-%H%M%S")
FULL_IMAGE_NAME="${DOCKER_REGISTRY}${DOCKER_IMAGE_NAME}:${DOCKER_IMAGE_TAG}"

log_info "开始部署流程..."
log_info "镜像名称: $FULL_IMAGE_NAME"
log_info "Docker Swarm名称: $DOCKER_SWARM_NAME"
log_info "部署文件: $DOCKER_COMPOSE_FILE"

# 检查部署文件是否存在
if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
  log_error "部署文件 '$DOCKER_COMPOSE_FILE' 不存在!"
  exit 1
fi

# 检查Dockerfile是否存在
if [ ! -f "Dockerfile" ]; then
  log_error "Dockerfile 不存在!"
  exit 1
fi
# 函数：构建Docker镜像
build_image() {
    log_info "开始构建Docker镜像: $FULL_IMAGE_NAME"

    # 构建镜像
    docker build -t "$FULL_IMAGE_NAME" .

    if [ $? -eq 0 ]; then
        log_success "镜像构建成功: $FULL_IMAGE_NAME"
    else
        log_error "镜像构建失败"
        exit 1
    fi

    # 如果有registry配置，推送镜像
    if [ -n "$DOCKER_REGISTRY" ]; then
        log_info "推送镜像到registry: $FULL_IMAGE_NAME"
        docker push "$FULL_IMAGE_NAME"
        if [ $? -eq 0 ]; then
            log_success "镜像推送成功"
        else
            log_error "镜像推送失败"
            exit 1
        fi
    fi
}

# 函数：清理旧镜像，保留最近的N个
cleanup_old_images() {
    log_info "清理旧镜像，保留最近的 $DOCKER_KEEP_IMAGES 个镜像"

    # 获取所有相关镜像，按创建时间排序
    local images=$(docker images "${DOCKER_REGISTRY}${DOCKER_IMAGE_NAME}" --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | grep -v "REPOSITORY" | sort -k2 -r)

    if [ -z "$images" ]; then
        log_warning "没有找到相关镜像"
        return
    fi

    # 计算需要删除的镜像
    local image_count=$(echo "$images" | wc -l)
    local images_to_delete=$((image_count - DOCKER_KEEP_IMAGES))

    if [ $images_to_delete -gt 0 ]; then
        log_info "发现 $image_count 个镜像，需要删除 $images_to_delete 个旧镜像"

        # 获取要删除的镜像列表（最旧的几个）
        local old_images=$(echo "$images" | tail -n $images_to_delete | awk '{print $1}')

        for image in $old_images; do
            log_info "删除旧镜像: $image"
            docker rmi "$image" 2>/dev/null || log_warning "无法删除镜像 $image (可能正在使用中)"
        done

        log_success "旧镜像清理完成"
    else
        log_info "镜像数量未超过限制，无需清理"
    fi
}

# 函数：部署到Docker Swarm
deploy_stack() {
    log_info "部署到Docker Swarm: $DOCKER_SWARM_NAME"

    # 设置镜像标签环境变量
    export DOCKER_IMAGE_TAG="$DOCKER_IMAGE_TAG"

    # 部署stack
    docker stack deploy -c "$DOCKER_COMPOSE_FILE" "$DOCKER_SWARM_NAME"
    
    # 等待服务部署完成
    log_info "正在等待服务部署完成..."
    sleep 5
    
    # 使用循环代替watch命令
    for i in {1..10}; do
#        clear
        log_info "服务部署状态 (刷新 $i/10):"
        docker service ls --filter name=$DOCKER_SWARM_NAME

        # 检查是否所有服务都已部署完成
        local pending=$(docker service ls --filter name=$DOCKER_SWARM_NAME | grep -v "REPLICAS" | awk '{split($4,a,"/"); if(a[1]!=a[2]) print $2}' | wc -l)
        if [ "$pending" -eq "0" ]; then
            log_success "所有服务已成功部署!"
            break
        fi

        sleep 2
    done

    if [ $? -eq 0 ]; then
        log_success "Stack部署成功: $DOCKER_SWARM_NAME"
    else
        log_error "Stack部署失败"
        exit 1
    fi
}

# 函数：显示部署状态
show_deployment_status() {
    log_info "检查部署状态..."

    echo ""
    log_info "Stack服务状态:"
    docker stack services "$DOCKER_SWARM_NAME"

#    echo ""
#    log_info "Stack任务状态:"
#    docker stack ps "$DOCKER_SWARM_NAME" --no-trunc
}

# 主执行流程
main() {
    log_info "=== 开始自动化部署流程 ==="

    # 1. 构建镜像
    build_image

    # 2. 清理旧镜像
    cleanup_old_images

    # 3. 部署到Swarm
    deploy_stack

    # 4. 显示部署状态
    show_deployment_status

    log_success "=== 部署流程完成 ==="
    log_info "镜像标签: $DOCKER_IMAGE_TAG"
    log_info "完整镜像名: $FULL_IMAGE_NAME"
}

# 执行主函数
main